'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Share2, Upload, Smartphone, Globe, Bookmark, Check, Copy } from 'lucide-react';

export default function HelpPage() {
  const router = useRouter();
  const [copiedBookmarklet, setCopiedBookmarklet] = useState(false);

  const bookmarkletCode = `javascript:(function(){const MEME_DB_URL='http://localhost:3000';const API_URL='http://127.0.0.1:3001';if(window.location.href.includes(MEME_DB_URL)){alert('You are already on the Meme Database!');return;}if(document.getElementById('memedb-drop-zone')){alert('MemeDB drop zone is already active! Drag images anywhere on the page to save them.');return;}const dropZone=document.createElement('div');dropZone.id='memedb-drop-zone';dropZone.style.cssText='position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(59,130,246,0.1);border:3px dashed #3b82f6;z-index:9999;display:none;align-items:center;justify-content:center;font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,sans-serif;pointer-events:auto';const dropText=document.createElement('div');dropText.style.cssText='background:#3b82f6;color:white;padding:30px 50px;border-radius:16px;font-size:28px;font-weight:bold;text-align:center;box-shadow:0 20px 40px rgba(0,0,0,0.3)';dropText.innerHTML='🎭 Drop image here to save to MemeDB!';dropZone.appendChild(dropText);document.body.appendChild(dropZone);function showMessage(text,color='#10b981'){const messageDiv=document.createElement('div');messageDiv.style.cssText='position:fixed;top:20px;right:20px;background:'+color+';color:white;padding:15px 20px;border-radius:8px;z-index:10001;font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,sans-serif;font-size:14px;font-weight:500;box-shadow:0 4px 12px rgba(0,0,0,0.15);max-width:320px';messageDiv.innerHTML=text;document.body.appendChild(messageDiv);setTimeout(()=>{if(document.body.contains(messageDiv)){document.body.removeChild(messageDiv);}},4000);}async function uploadImage(imageUrl){try{showMessage('📤 Uploading...','#3b82f6');const response=await fetch(API_URL+'/api/memes/upload',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({image_url:imageUrl,title:'Meme from '+window.location.hostname,tags:['bookmarklet','saved'],source_url:window.location.href})});const result=await response.json();if(result.success){showMessage('✅ Meme saved! <a href="'+MEME_DB_URL+'" target="_blank" style="color:white;text-decoration:underline">View →</a>');}else{throw new Error(result.message||'Failed to save');}}catch(error){showMessage('❌ Error: '+error.message,'#ef4444');}}async function uploadFile(file){try{showMessage('📤 Uploading '+file.name+'...','#3b82f6');const formData=new FormData();formData.append('image',file);formData.append('title',file.name+' from '+window.location.hostname);formData.append('tags',JSON.stringify(['bookmarklet','saved']));formData.append('source_url',window.location.href);const response=await fetch(API_URL+'/api/memes/upload',{method:'POST',body:formData});const result=await response.json();if(result.success){showMessage('✅ '+file.name+' saved! <a href="'+MEME_DB_URL+'" target="_blank" style="color:white;text-decoration:underline">View →</a>');}else{throw new Error(result.message||'Failed to save');}}catch(error){showMessage('❌ Error: '+error.message,'#ef4444');}}let dragCounter=0;function preventDefaults(e){e.preventDefault();e.stopPropagation();}function handleDragEnter(e){preventDefaults(e);dragCounter++;dropZone.style.display='flex';}function handleDragLeave(e){preventDefaults(e);dragCounter--;if(dragCounter===0){dropZone.style.display='none';}}function handleDragOver(e){preventDefaults(e);e.dataTransfer.dropEffect='copy';}function handleDrop(e){preventDefaults(e);dragCounter=0;dropZone.style.display='none';const dt=e.dataTransfer;const files=dt.files;if(files.length>0){Array.from(files).forEach(file=>{if(file.type.startsWith('image/')){uploadFile(file);}});return;}const htmlData=dt.getData('text/html');if(htmlData){const imgMatch=htmlData.match(/<img[^>]+src=['"](https?:\\/\\/[^'"]+)['"]/i);if(imgMatch){uploadImage(imgMatch[1]);return;}}const url=dt.getData('text/plain');if(url&&url.startsWith('http')&&(url.match(/\\.(jpg|jpeg|png|gif|webp)(\\?.*)?$/i)||url.includes('image'))){uploadImage(url);return;}showMessage('❌ Please drop an image','#ef4444');}document.addEventListener('dragenter',handleDragEnter);document.addEventListener('dragleave',handleDragLeave);document.addEventListener('dragover',handleDragOver);document.addEventListener('drop',handleDrop);showMessage('🎭 Drop zone active! Drag images here. Press ESC to deactivate.','#3b82f6');document.addEventListener('keydown',function(e){if(e.key==='Escape'){document.removeEventListener('dragenter',handleDragEnter);document.removeEventListener('dragleave',handleDragLeave);document.removeEventListener('dragover',handleDragOver);document.removeEventListener('drop',handleDrop);if(document.body.contains(dropZone)){document.body.removeChild(dropZone);}showMessage('🎭 Drop zone deactivated','#6b7280');}});})();`;

  const copyBookmarklet = () => {
    navigator.clipboard.writeText(bookmarkletCode).then(() => {
      setCopiedBookmarklet(true);
      setTimeout(() => setCopiedBookmarklet(false), 2000);
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <button
              onClick={() => router.push('/')}
              className="flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft size={20} />
              <span>Back to Memes</span>
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            🎭 How to Use MemeDB
          </h1>
          <p className="text-xl text-gray-600">
            Three powerful ways to save memes from anywhere on the web
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-3 gap-8 mb-12">
          {/* Bookmarklet */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="text-center mb-4">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <Bookmark className="text-blue-600" size={24} />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Drag & Drop Bookmarklet</h3>
              <p className="text-sm text-gray-600">Save images by dragging</p>
            </div>
            
            <div className="space-y-3">
              <div className="bg-gray-50 p-3 rounded border">
                <p className="text-xs text-gray-600 mb-2">Drag this button to your bookmarks bar:</p>
                <a 
                  href={bookmarkletCode}
                  className="flex items-center justify-center gap-2 bg-blue-600 text-white px-3 py-2 rounded text-sm font-medium hover:bg-blue-700 transition-colors"
                  title="Drag this to your bookmarks bar, then click it on any website to activate drag & drop saving"
                >
                  <span style={{fontSize: '20px'}}>🎭</span>
                  <span>Save to MemeDB</span>
                </a>
                <p className="text-xs text-gray-500 mt-2">
                  <strong>How it works:</strong> Click the bookmark on any website to activate a drop zone. 
                  Then drag images from that page (or your computer) anywhere on the page to save them instantly!
                </p>
              </div>
              
              <button
                onClick={copyBookmarklet}
                className="w-full flex items-center justify-center gap-2 text-sm text-gray-600 hover:text-gray-800"
              >
                {copiedBookmarklet ? (
                  <>
                    <Check size={14} />
                    Copied!
                  </>
                ) : (
                  <>
                    <Copy size={14} />
                    Copy Code
                  </>
                )}
              </button>

              <div className="text-xs text-gray-500 space-y-1">
                <p>• Drag this button to your bookmarks bar</p>
                <p>• Click it on any website to activate</p>
                <p>• Drag images from any site onto the page</p>
                <p>• Images save instantly to MemeDB</p>
                <p>• Works on Reddit, Twitter, Instagram, etc.</p>
              </div>
            </div>
          </div>

          {/* Drag & Drop */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="text-center mb-4">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <Upload className="text-green-600" size={24} />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Drag & Drop</h3>
              <p className="text-sm text-gray-600">Direct upload anywhere</p>
            </div>
            
            <div className="text-xs text-gray-500 space-y-1">
              <p>• Drag images from any folder</p>
              <p>• Drop anywhere on the page</p>
              <p>• Paste with Ctrl+V (Cmd+V)</p>
              <p>• Auto-opens upload dialog</p>
              <p>• Supports PNG, JPG, GIF</p>
            </div>
          </div>

          {/* Mobile Share */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="text-center mb-4">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <Share2 className="text-purple-600" size={24} />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Mobile Share</h3>
              <p className="text-sm text-gray-600">Share from any app</p>
            </div>
            
            <div className="text-xs text-gray-500 space-y-1">
              <p>• Appears in mobile share menu</p>
              <p>• Share from Photos, Camera, etc.</p>
              <p>• Install as PWA app first</p>
              <p>• Works on Android & iOS</p>
              <p>• Auto-fills title and tags</p>
            </div>
          </div>
        </div>

        {/* Mobile PWA Instructions */}
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-6 mb-8">
          <div className="flex items-start gap-4">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
              <Smartphone className="text-blue-600" size={20} />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">📱 Install as Mobile App</h3>
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium text-gray-800 mb-2">Android (Chrome):</h4>
                  <ol className="text-sm text-gray-600 space-y-1 list-decimal list-inside">
                    <li>Tap the menu (⋮) in Chrome</li>
                    <li>Tap &quot;Add to Home screen&quot;</li>
                    <li>Confirm the installation</li>
                    <li>Now appears in share menus!</li>
                  </ol>
                </div>
                <div>
                  <h4 className="font-medium text-gray-800 mb-2">iOS (Safari):</h4>
                  <ol className="text-sm text-gray-600 space-y-1 list-decimal list-inside">
                    <li>Tap the Share button (□↗)</li>
                    <li>Scroll down and tap &quot;Add to Home Screen&quot;</li>
                    <li>Tap &quot;Add&quot; to confirm</li>
                    <li>Available in iOS share sheet!</li>
                  </ol>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Start */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
            <Globe size={20} />
            Quick Start Guide
          </h3>
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-gray-800 mb-2">Desktop Users:</h4>
              <ol className="text-sm text-gray-600 space-y-1 list-decimal list-inside">
                <li>Drag the blue button to your bookmarks bar</li>
                <li>Visit any website with images (Reddit, Twitter, etc.)</li>
                <li>Click the &quot;🎭 Save to MemeDB&quot; bookmark to activate</li>
                <li>Drag images from the page anywhere to save them</li>
                <li>Or drag image files from your computer</li>
              </ol>
            </div>
            <div>
              <h4 className="font-medium text-gray-800 mb-2">Mobile Users:</h4>
              <ol className="text-sm text-gray-600 space-y-1 list-decimal list-inside">
                <li>Install MemeDB as a mobile app</li>
                <li>Open any app with images</li>
                <li>Tap the Share button</li>
                <li>Select &quot;MemeDB&quot; from the list</li>
              </ol>
            </div>
          </div>
        </div>

        {/* Tips */}
        <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h4 className="font-semibold text-yellow-800 mb-2">💡 Pro Tips</h4>
          <ul className="text-sm text-yellow-700 space-y-1">
            <li>• The bookmarklet creates a drop zone that works with any image drag</li>
            <li>• You can drag images from other browser tabs or your desktop</li>
            <li>• Press ESC to deactivate the drop zone when you&apos;re done</li>
            <li>• Tags are auto-generated using AI if you leave them empty</li>
            <li>• The app works offline once installed as a PWA</li>
            <li>• Shared content automatically pre-fills the title and tags</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
